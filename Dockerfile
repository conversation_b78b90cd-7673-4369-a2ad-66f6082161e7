# Week 5 Production Dockerfile - Enhanced Search, RBAC, Redis Rate Limiting
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies including curl for health checks
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements-week4.txt .

# Install Python dependencies with timeout and retry settings
RUN pip install --no-cache-dir --timeout 300 --retries 3 -r requirements-week4.txt

# Copy application code
COPY src/ ./src/
COPY docs/ ./docs/
COPY examples/ ./examples/

# Create necessary directories
RUN mkdir -p logs

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Run the basic Week 4 application for staging
CMD ["uvicorn", "src.api.main_basic:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]
